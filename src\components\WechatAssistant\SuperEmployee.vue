<template>
  <view class="super-employee-container">
    <!-- 头部统计信息 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stats-item">
          <text class="stats-number">{{ totalEmployees }}</text>
          <text class="stats-label">总员工数</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{ activeEmployees }}</text>
          <text class="stats-label">运行中</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{ todayTasks }}</text>
          <text class="stats-label">今日任务</text>
        </view>
      </view>
    </view>

    <!-- 员工列表 -->
    <view class="employee-list-section">
      <view class="section-header">
        <text class="section-title">我的超级员工</text>
        <TnButton
          @click="handleCreateEmployee"
          bg-color="#f1c68e"
          text-color="#634738"
          size="sm"
          width="140rpx"
          height="60rpx"
          class="create-btn"
        >
          <TnIcon name="plus" size="16" color="#634738" />
          <text class="btn-text">新建</text>
        </TnButton>
      </view>

      <!-- 员工卡片列表 -->
      <view v-if="employees.length > 0" class="employee-cards">
        <view
          v-for="employee in employees"
          :key="employee._id"
          class="employee-card"
          @click="handleEmployeeDetail(employee)"
        >
          <!-- 员工头部信息 -->
          <view class="card-header">
            <view class="employee-info">
              <view class="employee-avatar">
                <TnIcon :name="getEmployeeIcon(employee.type)" size="24" color="#f1c68e" />
              </view>
              <view class="employee-basic">
                <text class="employee-name">{{ employee.name }}</text>
                <text class="employee-type">{{ getEmployeeTypeName(employee.type) }}</text>
              </view>
            </view>
            <view class="employee-status">
              <view
                class="status-dot"
                :class="{ active: employee.status === 'active' }"
              ></view>
              <text class="status-text">{{ employee.status === 'active' ? '运行中' : '已停止' }}</text>
            </view>
          </view>

          <!-- 员工详细信息 -->
          <view class="card-content">
            <view class="info-row">
              <text class="info-label">执行频率：</text>
              <text class="info-value">{{ getFrequencyText(employee.frequency) }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">下次执行：</text>
              <text class="info-value">{{ getNextExecutionTime(employee) }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">累计任务：</text>
              <text class="info-value">{{ employee.total_tasks || 0 }}次</text>
            </view>
          </view>

          <!-- 员工操作按钮 -->
          <view class="card-actions">
            <TnButton
              @click.stop="handleTriggerTask(employee)"
              bg-color="rgba(81, 207, 102, 0.2)"
              text-color="#51cf66"
              size="sm"
              width="100rpx"
              height="50rpx"
            >
              执行
            </TnButton>
            <TnButton
              @click.stop="handleToggleStatus(employee)"
              :bg-color="employee.status === 'active' ? '#ff6b6b' : '#51cf66'"
              text-color="#fff"
              size="sm"
              width="80rpx"
              height="50rpx"
            >
              {{ employee.status === 'active' ? '停止' : '启动' }}
            </TnButton>
            <TnButton
              @click.stop="handleEditEmployee(employee)"
              bg-color="rgba(241, 198, 142, 0.2)"
              text-color="#f1c68e"
              size="sm"
              width="80rpx"
              height="50rpx"
            >
              编辑
            </TnButton>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <TnIcon name="robot" size="80" color="rgba(255,255,255,0.3)" />
        <text class="empty-title">还没有超级员工</text>
        <text class="empty-desc">创建您的第一个AI员工，让它帮您自动化工作</text>
        <TnButton
          @click="handleCreateEmployee"
          bg-color="#f1c68e"
          text-color="#634738"
          size="lg"
          width="300rpx"
          height="80rpx"
          class="empty-create-btn"
        >
          立即创建
        </TnButton>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-overlay">
      <TnIcon name="loading" size="40" color="#f1c68e" class="loading-icon" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 创建员工模态对话框 -->
    <view v-if="showCreateModal" class="modal-overlay" @click="handleCloseModal">
      <view class="modal-container" @click.stop>
        <!-- 对话框头部 -->
        <view class="modal-header">
          <text class="modal-title">{{ currentStep === 1 ? '选择员工类型' : '配置员工信息' }}</text>
          <TnIcon
            name="close"
            size="24"
            color="rgba(255,255,255,0.6)"
            @click="handleCloseModal"
            class="close-icon"
          />
        </view>

        <!-- 步骤1：选择员工类型 -->
        <view v-if="currentStep === 1" class="modal-content">
          <view class="employee-types">
            <view
              v-for="template in employeeTemplates"
              :key="template.id"
              class="employee-type-card"
              :class="{ selected: selectedTemplate?.id === template.id }"
              @click="handleSelectTemplate(template)"
            >
              <view class="type-avatar">{{ template.avatar }}</view>
              <view class="type-info">
                <text class="type-name">{{ template.name }}</text>
                <text class="type-title">{{ template.title }}</text>
                <text class="type-description">{{ template.description }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 步骤2：配置员工信息 -->
        <view v-if="currentStep === 2" class="modal-content">
          <scroll-view class="config-form" scroll-y>
            <!-- 运行时间设置 -->
            <view class="form-section">
              <text class="section-title">运行时间设置</text>
              <view class="form-item">
                <text class="form-label">执行时间</text>
                <view class="time-selector">
                  <view
                    v-for="(time, index) in employeeConfig.executionTimes"
                    :key="index"
                    class="time-item"
                  >
                    <TnTimePicker
                      v-model="employeeConfig.executionTimes[index]"
                      format="HH:mm"
                      bg-color="rgba(255,255,255,0.05)"
                      text-color="#fff"
                      border-color="rgba(255,255,255,0.1)"
                      class="time-picker"
                    />
                    <TnButton
                      v-if="employeeConfig.executionTimes.length > 1"
                      @click="removeExecutionTime(index)"
                      bg-color="#ff6b6b"
                      text-color="#fff"
                      size="sm"
                      width="60rpx"
                      height="60rpx"
                      class="remove-time-btn"
                    >
                      <TnIcon name="minus" size="16" />
                    </TnButton>
                  </view>
                  <TnButton
                    v-if="employeeConfig.executionTimes.length < 3"
                    @click="addExecutionTime"
                    bg-color="rgba(241, 198, 142, 0.2)"
                    text-color="#f1c68e"
                    size="sm"
                    width="120rpx"
                    height="60rpx"
                    class="add-time-btn"
                  >
                    <TnIcon name="plus" size="16" />
                    <text>添加时间</text>
                  </TnButton>
                </view>
              </view>
            </view>

            <!-- 同步公众号 -->
            <view class="form-section">
              <text class="section-title">同步公众号</text>
              <view class="form-item">
                <view v-if="wechatAccounts.length > 0" class="account-list">
                  <view
                    v-for="account in wechatAccounts"
                    :key="account._id"
                    class="account-item"
                    :class="{ selected: employeeConfig.selectedAccounts.includes(account._id) }"
                    @click="toggleAccountSelection(account._id)"
                  >
                    <view class="account-info">
                      <text class="account-name">{{ account.name }}</text>
                      <text class="account-status">{{ account.status === 'active' ? '正常' : '异常' }}</text>
                    </view>
                    <TnIcon
                      :name="employeeConfig.selectedAccounts.includes(account._id) ? 'checkbox-checked' : 'checkbox'"
                      size="20"
                      :color="employeeConfig.selectedAccounts.includes(account._id) ? '#f1c68e' : 'rgba(255,255,255,0.4)'"
                    />
                  </view>
                </view>
                <view v-else class="no-accounts">
                  <text class="no-accounts-text">暂无绑定的公众号</text>
                  <TnButton
                    @click="handleGoToAccountManage"
                    bg-color="rgba(241, 198, 142, 0.2)"
                    text-color="#f1c68e"
                    size="sm"
                    width="120rpx"
                    height="50rpx"
                  >
                    去绑定
                  </TnButton>
                </view>
              </view>
            </view>

            <!-- 员工备注 -->
            <view class="form-section">
              <text class="section-title">员工备注</text>
              <view class="form-item">
                <TnTextarea
                  v-model="employeeConfig.remark"
                  placeholder="为这个员工添加备注说明（可选）"
                  :maxlength="200"
                  :show-count="true"
                  bg-color="rgba(255,255,255,0.05)"
                  text-color="#fff"
                  placeholder-color="rgba(255,255,255,0.4)"
                  border-color="rgba(255,255,255,0.1)"
                  focus-border-color="#f1c68e"
                  class="remark-textarea"
                />
              </view>
            </view>
          </scroll-view>
        </view>

        <!-- 对话框底部按钮 -->
        <view class="modal-footer">
          <TnButton
            @click="handleCloseModal"
            bg-color="rgba(255,255,255,0.1)"
            text-color="rgba(255,255,255,0.8)"
            size="lg"
            width="200rpx"
            height="80rpx"
            class="cancel-btn"
          >
            取消
          </TnButton>
          <TnButton
            v-if="currentStep === 1"
            @click="handleNextStep"
            :disabled="!selectedTemplate"
            bg-color="#f1c68e"
            text-color="#634738"
            size="lg"
            width="200rpx"
            height="80rpx"
            class="next-btn"
          >
            下一步
          </TnButton>
          <TnButton
            v-if="currentStep === 2"
            @click="handleCreateConfirm"
            :loading="creating"
            bg-color="#f1c68e"
            text-color="#634738"
            size="lg"
            width="200rpx"
            height="80rpx"
            class="create-btn"
          >
            确认创建
          </TnButton>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import TnTimePicker from '@tuniao/tnui-vue3-uniapp/components/time-picker/src/time-picker.vue'
import TnTextarea from '@tuniao/tnui-vue3-uniapp/components/textarea/src/textarea.vue'

const vk = uni.vk

// 响应式数据
const loading = ref(false)
const employees = ref([])

// 模态对话框相关
const showCreateModal = ref(false)
const currentStep = ref(1) // 1: 选择类型, 2: 配置信息
const creating = ref(false)

// 员工模板和配置
const employeeTemplates = ref<any[]>([])
const selectedTemplate = ref<any>(null)
const wechatAccounts = ref<any[]>([])

// 员工配置表单
const employeeConfig = ref({
  executionTimes: ['09:00'] as string[],
  selectedAccounts: [] as string[],
  remark: ''
})

// 计算属性
const totalEmployees = computed(() => employees.value.length)
const activeEmployees = computed(() => employees.value.filter(emp => emp.status === 'active').length)
const todayTasks = computed(() => {
  // 这里后续会从后端获取今日任务数量
  return 0
})

// 获取员工类型图标
const getEmployeeIcon = (type: string) => {
  const iconMap = {
    news: 'news',
    wallpaper: 'image',
    article: 'edit',
    social: 'share',
    default: 'robot'
  }
  return iconMap[type] || iconMap.default
}

// 获取员工类型名称
const getEmployeeTypeName = (type: string) => {
  const typeMap = {
    news: '新闻采集员',
    wallpaper: '壁纸生成员',
    article: '文章写手',
    social: '社交媒体员',
    default: '通用员工'
  }
  return typeMap[type] || typeMap.default
}

// 获取频率文本
const getFrequencyText = (frequency: any) => {
  if (!frequency) return '未设置'
  
  const { type, value } = frequency
  const typeMap = {
    daily: '每日',
    weekly: '每周',
    monthly: '每月'
  }
  
  return `${typeMap[type] || ''}${value ? ` ${value}次` : ''}`
}

// 获取下次执行时间
const getNextExecutionTime = (employee: any) => {
  if (!employee.next_execution_time) return '未设置'
  
  const nextTime = new Date(employee.next_execution_time)
  const now = new Date()
  
  if (nextTime < now) return '待执行'
  
  return nextTime.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 事件处理
const handleCreateEmployee = async () => {
  console.log('创建新员工')

  try {
    // 加载员工模板和微信账号
    await loadEmployeeTemplates()
    await loadWechatAccounts()

    // 重置表单状态
    resetCreateForm()

    // 显示模态对话框
    showCreateModal.value = true
  } catch (error) {
    console.error('加载创建数据失败:', error)
    vk.toast('加载失败，请重试')
  }
}

const handleEmployeeDetail = (employee: any) => {
  console.log('查看员工详情:', employee)
  vk.navigateTo({
    url: `/pages/tools/super-employee-detail?id=${employee._id}&mode=view`
  })
}

const handleEditEmployee = (employee: any) => {
  console.log('编辑员工:', employee)
  vk.navigateTo({
    url: `/pages/tools/super-employee-detail?id=${employee._id}&mode=edit`
  })
}

const handleTriggerTask = async (employee: any) => {
  console.log('手动触发员工任务:', employee)

  try {
    loading.value = true
    vk.showLoading('正在执行任务...')

    // 调用云函数触发任务
    const res = await vk.callFunction({
      url: 'client/mx/superEmployee/kh/triggerEmployeeTask',
      data: {
        employee_id: employee._id
      }
    })

    if (res.code === 0) {
      vk.toast('任务执行成功')
      // 刷新员工列表以更新统计信息
      getEmployeeList()
    } else {
      vk.toast(res.msg || '任务执行失败')
    }
  } catch (error) {
    console.error('触发员工任务失败:', error)
    vk.toast('任务执行失败，请重试')
  } finally {
    loading.value = false
    vk.hideLoading()
  }
}

const handleToggleStatus = async (employee: any) => {
  console.log('切换员工状态:', employee)

  try {
    loading.value = true

    const newStatus = employee.status === 'active' ? 'inactive' : 'active'

    // 调用云函数更新状态
    const res = await vk.callFunction({
      url: 'client/mx/superEmployee/kh/updateEmployeeStatus',
      data: {
        employee_id: employee._id,
        status: newStatus
      }
    })

    if (res.code === 0) {
      employee.status = newStatus
      vk.toast(newStatus === 'active' ? '员工已启动' : '员工已停止')
    } else {
      vk.toast(res.msg || '操作失败')
    }
  } catch (error) {
    console.error('切换员工状态失败:', error)
    vk.toast('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

// 加载员工模板
const loadEmployeeTemplates = async () => {
  try {
    // 暂时使用硬编码的壁纸助手模板，后续可以从后端获取
    employeeTemplates.value = [
      {
        id: 'wallpaper_assistant',
        type: 'wallpaper',
        name: '小美',
        title: '壁纸助手',
        avatar: '🎨',
        description: '我是专业的壁纸助手，每天为您推荐精美壁纸，分享设计美学和创意灵感。'
      }
    ]
  } catch (error) {
    console.error('加载员工模板失败:', error)
    throw error
  }
}

// 加载微信公众号账号
const loadWechatAccounts = async () => {
  try {
    const res = await vk.callFunction({
      url: 'client/mx/wechat/kh/getAccountList',
      data: {}
    })

    if (res.code === 0) {
      wechatAccounts.value = res.data || []
    } else {
      console.error('获取微信账号失败:', res.msg)
      wechatAccounts.value = []
    }
  } catch (error) {
    console.error('获取微信账号失败:', error)
    wechatAccounts.value = []
  }
}

// 重置创建表单
const resetCreateForm = () => {
  currentStep.value = 1
  selectedTemplate.value = null
  employeeConfig.value = {
    executionTimes: ['09:00'],
    selectedAccounts: [],
    remark: ''
  }
}

// 获取员工列表
const getEmployeeList = async () => {
  try {
    loading.value = true

    const res = await vk.callFunction({
      url: 'client/mx/superEmployee/kh/getEmployeeList',
      data: {}
    })

    if (res.code === 0) {
      employees.value = res.data || []
    } else {
      console.error('获取员工列表失败:', res.msg)
    }
  } catch (error) {
    console.error('获取员工列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 模态对话框相关方法
const handleCloseModal = () => {
  showCreateModal.value = false
  resetCreateForm()
}

const handleSelectTemplate = (template: any) => {
  selectedTemplate.value = template
}

const handleNextStep = () => {
  if (!selectedTemplate.value) {
    vk.toast('请选择员工类型')
    return
  }
  currentStep.value = 2
}

const addExecutionTime = () => {
  if (employeeConfig.value.executionTimes.length < 3) {
    employeeConfig.value.executionTimes.push('12:00')
  }
}

const removeExecutionTime = (index: number) => {
  if (employeeConfig.value.executionTimes.length > 1) {
    employeeConfig.value.executionTimes.splice(index, 1)
  }
}

const toggleAccountSelection = (accountId: string) => {
  const index = employeeConfig.value.selectedAccounts.indexOf(accountId)
  if (index > -1) {
    employeeConfig.value.selectedAccounts.splice(index, 1)
  } else {
    employeeConfig.value.selectedAccounts.push(accountId)
  }
}

const handleGoToAccountManage = () => {
  // 关闭模态框并切换到账号管理标签页
  handleCloseModal()
  // 这里需要通知父组件切换标签页
  uni.$emit('switchToAccountManage')
}

const handleCreateConfirm = async () => {
  if (!selectedTemplate.value) {
    vk.toast('请选择员工类型')
    return
  }

  if (employeeConfig.value.executionTimes.length === 0) {
    vk.toast('请设置至少一个执行时间')
    return
  }

  try {
    creating.value = true

    // 构建员工配置
    const config = {
      frequency: {
        type: 'daily',
        value: employeeConfig.value.executionTimes.length,
        times: employeeConfig.value.executionTimes
      },
      ai_config: {
        system_prompt: '你是一位专业的壁纸助手，名叫小美。你对美有着独特的见解和敏锐的感知力，擅长从色彩、构图、意境等角度分析和描述图片的美感。你的文字优美生动，能够让读者感受到视觉艺术的魅力。请创作关于壁纸、美图、设计的文章，要求语言优美、富有感染力、能够激发读者的美感体验。',
        temperature: 0.8,
        max_tokens: 1500
      },
      content_config: {
        keywords: ['精美壁纸', '设计美学', '色彩搭配', '视觉艺术', '创意灵感'],
        filters: {
          min_length: 500,
          max_length: 1500,
          exclude_keywords: []
        }
      },
      publish_config: {
        auto_publish: employeeConfig.value.selectedAccounts.length > 0,
        wechat_accounts: employeeConfig.value.selectedAccounts,
        format_style: 'creative',
        auto_generate_images: true
      }
    }

    // 调用创建员工API
    const res = await vk.callFunction({
      url: 'client/mx/superEmployee/kh/createEmployee',
      data: {
        name: selectedTemplate.value.name,
        type: selectedTemplate.value.type,
        description: employeeConfig.value.remark || selectedTemplate.value.description,
        config: config
      }
    })

    if (res.code === 0) {
      vk.toast('员工创建成功')
      handleCloseModal()
      // 刷新员工列表
      getEmployeeList()
    } else {
      vk.toast(res.msg || '创建失败')
    }
  } catch (error) {
    console.error('创建员工失败:', error)
    vk.toast('创建失败，请重试')
  } finally {
    creating.value = false
  }
}

// 生命周期
onMounted(() => {
  getEmployeeList()
})
</script>

<style lang="scss" scoped>
.super-employee-container {
  min-height: 100%;
  position: relative;

  // 统计信息区域
  .stats-section {
    margin-bottom: 30rpx;

    .stats-card {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 16rpx;
      padding: 30rpx;
      display: flex;
      justify-content: space-around;
      border: 1px solid rgba(255, 255, 255, 0.1);

      .stats-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8rpx;

        .stats-number {
          font-size: 48rpx;
          font-weight: 700;
          color: #f1c68e;
        }

        .stats-label {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }
  }

  // 员工列表区域
  .employee-list-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;

      .section-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #fff;
      }

      .create-btn {
        display: flex;
        align-items: center;
        gap: 8rpx;
        border-radius: 30rpx !important;

        .btn-text {
          font-size: 24rpx;
        }
      }
    }

    // 员工卡片
    .employee-cards {
      display: flex;
      flex-direction: column;
      gap: 20rpx;

      .employee-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 16rpx;
        padding: 30rpx;
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.98);
          background: rgba(255, 255, 255, 0.08);
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20rpx;

          .employee-info {
            display: flex;
            align-items: center;
            gap: 20rpx;

            .employee-avatar {
              width: 80rpx;
              height: 80rpx;
              background: rgba(241, 198, 142, 0.2);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .employee-basic {
              display: flex;
              flex-direction: column;
              gap: 8rpx;

              .employee-name {
                font-size: 32rpx;
                font-weight: 600;
                color: #fff;
              }

              .employee-type {
                font-size: 24rpx;
                color: rgba(255, 255, 255, 0.6);
              }
            }
          }

          .employee-status {
            display: flex;
            align-items: center;
            gap: 12rpx;

            .status-dot {
              width: 16rpx;
              height: 16rpx;
              border-radius: 50%;
              background: #ff6b6b;

              &.active {
                background: #51cf66;
              }
            }

            .status-text {
              font-size: 24rpx;
              color: rgba(255, 255, 255, 0.8);
            }
          }
        }

        .card-content {
          margin-bottom: 20rpx;

          .info-row {
            display: flex;
            align-items: center;
            margin-bottom: 12rpx;

            .info-label {
              font-size: 26rpx;
              color: rgba(255, 255, 255, 0.6);
              width: 160rpx;
            }

            .info-value {
              font-size: 26rpx;
              color: #fff;
              flex: 1;
            }
          }
        }

        .card-actions {
          display: flex;
          gap: 20rpx;
          justify-content: flex-end;
        }
      }
    }

    // 空状态
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 100rpx 0;
      gap: 30rpx;

      .empty-title {
        font-size: 36rpx;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.8);
      }

      .empty-desc {
        font-size: 28rpx;
        color: rgba(255, 255, 255, 0.5);
        text-align: center;
        line-height: 1.5;
      }

      .empty-create-btn {
        border-radius: 40rpx !important;
      }
    }
  }

  // 加载状态
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(26, 31, 43, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20rpx;

    .loading-icon {
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 模态对话框样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 40rpx;
}

.modal-container {
  background: linear-gradient(135deg, #1A1F2B 0%, #2D3748 100%);
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .modal-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #fff;
  }

  .close-icon {
    padding: 10rpx;
    cursor: pointer;
  }
}

.modal-content {
  flex: 1;
  overflow: hidden;
  padding: 30rpx;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1px solid rgba(255, 255, 255, 0.1);

  .cancel-btn,
  .next-btn,
  .create-btn {
    border-radius: 40rpx !important;
  }
}

// 员工类型选择样式
.employee-types {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.employee-type-card {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 25rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16rpx;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;

  &.selected {
    border-color: #f1c68e;
    background: rgba(241, 198, 142, 0.1);
  }

  &:hover {
    background: rgba(255, 255, 255, 0.08);
  }

  .type-avatar {
    font-size: 48rpx;
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(241, 198, 142, 0.2);
    border-radius: 50%;
  }

  .type-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;

    .type-name {
      font-size: 28rpx;
      font-weight: 600;
      color: #fff;
    }

    .type-title {
      font-size: 24rpx;
      color: #f1c68e;
    }

    .type-description {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.7);
      line-height: 1.4;
    }
  }
}

// 配置表单样式
.config-form {
  max-height: 400rpx;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 40rpx;

  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #f1c68e;
    margin-bottom: 20rpx;
    display: block;
  }
}

.form-item {
  margin-bottom: 25rpx;

  .form-label {
    font-size: 26rpx;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 15rpx;
    display: block;
  }
}

.time-selector {
  display: flex;
  flex-direction: column;
  gap: 15rpx;

  .time-item {
    display: flex;
    align-items: center;
    gap: 15rpx;

    .time-picker {
      flex: 1;
    }

    .remove-time-btn {
      border-radius: 50% !important;
    }
  }

  .add-time-btn {
    align-self: flex-start;
    display: flex;
    align-items: center;
    gap: 8rpx;
    border-radius: 30rpx !important;
  }
}

.account-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.account-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
  border: 1px solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;

  &.selected {
    border-color: #f1c68e;
    background: rgba(241, 198, 142, 0.1);
  }

  &:hover {
    background: rgba(255, 255, 255, 0.08);
  }

  .account-info {
    display: flex;
    flex-direction: column;
    gap: 5rpx;

    .account-name {
      font-size: 26rpx;
      color: #fff;
      font-weight: 500;
    }

    .account-status {
      font-size: 22rpx;
      color: rgba(255, 255, 255, 0.6);
    }
  }
}

.no-accounts {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  padding: 40rpx;
  text-align: center;

  .no-accounts-text {
    font-size: 26rpx;
    color: rgba(255, 255, 255, 0.6);
  }
}

.remark-textarea {
  width: 100%;
  border-radius: 12rpx;
}
</style>
